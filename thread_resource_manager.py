"""
Thread Resource Manager

This module manages resources for individual threads in the multithreaded GMX account creation system.
It handles browser instances, proxy assignments, cleanup procedures, and memory management.
"""

import threading
import logging
import time
import gc
import psutil
import os
from typing import Dict, Optional, Any, Set, TYPE_CHECKING
from dataclasses import dataclass
from datetime import datetime

from proxy_manager import ProxyManager
from captcha_coordinator import captcha_coordinator

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from gmx import Driver


@dataclass
class ThreadResources:
    """Container for thread-specific resources"""
    thread_id: int
    thread_name: str
    driver_instance: Optional['Driver'] = None
    proxy_config: Optional[Dict[str, str]] = None
    proxy_id: Optional[str] = None
    browser_pid: Optional[int] = None
    memory_usage_mb: float = 0.0
    created_at: datetime = None
    last_cleanup: Optional[datetime] = None
    cleanup_count: int = 0
    is_active: bool = True


def _get_driver_class():
    """Lazy import of Driver class to avoid circular imports"""
    from gmx import Driver
    return Driver


class ThreadResourceManager:
    """
    Manages resources for individual threads in multithreaded GMX account creation
    """
    
    def __init__(self, proxy_manager: ProxyManager):
        """
        Initialize the thread resource manager
        
        Args:
            proxy_manager: Thread-safe proxy manager instance
        """
        self.proxy_manager = proxy_manager
        self.logger = logging.getLogger(f"{__name__}.ThreadResourceManager")
        
        # Thread resource tracking
        self._resources_lock = threading.RLock()
        self._thread_resources: Dict[int, ThreadResources] = {}
        self._active_threads: Set[int] = set()
        
        # Resource limits and monitoring
        self.max_memory_per_thread_mb = 1024  # 1GB per thread
        self.cleanup_interval_seconds = 300   # 5 minutes
        self.max_browser_instances = 10       # Safety limit

        # Cleanup monitoring thread
        self._cleanup_thread: Optional[threading.Thread] = None
        self._stop_cleanup = threading.Event()

        # Captcha coordination
        self.captcha_coordinator = captcha_coordinator
        
        self.logger.info("ThreadResourceManager initialized")
    
    def allocate_thread_resources(self, thread_id: int, thread_name: str) -> ThreadResources:
        """
        Allocate resources for a new thread
        
        Args:
            thread_id: Unique thread identifier
            thread_name: Human-readable thread name
            
        Returns:
            ThreadResources object containing allocated resources
        """
        with self._resources_lock:
            if thread_id in self._thread_resources:
                self.logger.warning(f"Resources already allocated for thread {thread_id}")
                return self._thread_resources[thread_id]
            
            # Check resource limits
            if len(self._thread_resources) >= self.max_browser_instances:
                raise RuntimeError(f"Maximum browser instances ({self.max_browser_instances}) reached")
            
            # Create resource container
            resources = ThreadResources(
                thread_id=thread_id,
                thread_name=thread_name,
                created_at=datetime.now()
            )
            
            try:
                # Allocate proxy with thread-specific extension
                proxy_config = self.proxy_manager.rotate_proxy(strategy="least_used", reserve=True, thread_id=thread_id)
                if proxy_config:
                    resources.proxy_config = proxy_config
                    resources.proxy_id = proxy_config['id']
                    self.logger.info(f"Thread {thread_id}: Allocated proxy {proxy_config['id']}")
                else:
                    self.logger.warning(f"Thread {thread_id}: No proxy available")
                
                # Store resources
                self._thread_resources[thread_id] = resources
                self._active_threads.add(thread_id)
                
                self.logger.info(f"Thread {thread_id}: Resources allocated successfully")
                return resources
                
            except Exception as e:
                self.logger.error(f"Thread {thread_id}: Failed to allocate resources: {str(e)}")
                # Cleanup partial allocation
                if resources.proxy_id:
                    self.proxy_manager.release_proxy_reservation(resources.proxy_id)
                raise
    
    def create_browser_instance(self, thread_id: int, first_name: str, last_name: str,
                              birthday: str, password: str) -> Optional['Driver']:
        """
        Create a browser instance for the specified thread
        
        Args:
            thread_id: Thread identifier
            first_name: User first name
            last_name: User last name
            birthday: User birthday
            password: User password
            
        Returns:
            Driver instance or None if creation failed
        """
        with self._resources_lock:
            if thread_id not in self._thread_resources:
                self.logger.error(f"Thread {thread_id}: No resources allocated")
                return None
            
            resources = self._thread_resources[thread_id]
            
            if resources.driver_instance:
                self.logger.warning(f"Thread {thread_id}: Browser instance already exists")
                return resources.driver_instance
            
            try:
                self.logger.info(f"Thread {thread_id}: Creating browser instance")
                
                # Create driver instance with thread-specific index
                Driver = _get_driver_class()
                driver_instance = Driver(first_name, last_name, birthday, password, thread_id)
                
                # Store browser process ID for monitoring
                if hasattr(driver_instance, 'browser') and hasattr(driver_instance.browser, 'driver'):
                    try:
                        # Try to get the browser process ID
                        service = getattr(driver_instance.browser.driver, 'service', None)
                        if service and hasattr(service, 'process') and service.process:
                            resources.browser_pid = service.process.pid
                            self.logger.info(f"Thread {thread_id}: Browser PID: {resources.browser_pid}")
                    except Exception as pid_error:
                        self.logger.debug(f"Thread {thread_id}: Could not get browser PID: {str(pid_error)}")
                
                resources.driver_instance = driver_instance
                resources.memory_usage_mb = self._get_thread_memory_usage(thread_id)
                
                self.logger.info(f"Thread {thread_id}: Browser instance created successfully")
                return driver_instance
                
            except Exception as e:
                self.logger.error(f"Thread {thread_id}: Failed to create browser instance: {str(e)}")
                return None
    
    def cleanup_thread_resources(self, thread_id: int, force: bool = False):
        """
        Clean up resources for a specific thread
        
        Args:
            thread_id: Thread identifier
            force: Force cleanup even if thread is still active
        """
        with self._resources_lock:
            if thread_id not in self._thread_resources:
                self.logger.debug(f"Thread {thread_id}: No resources to cleanup")
                return
            
            resources = self._thread_resources[thread_id]
            
            if resources.is_active and not force:
                self.logger.debug(f"Thread {thread_id}: Skipping cleanup - thread still active")
                return
            
            self.logger.info(f"Thread {thread_id}: Starting resource cleanup")
            
            try:
                # Cleanup browser instance
                if resources.driver_instance:
                    try:
                        self.logger.info(f"Thread {thread_id}: Cleaning up browser instance")
                        resources.driver_instance.terminate_selenium_driver()
                        
                        # Additional process cleanup if we have PID
                        if resources.browser_pid:
                            self._cleanup_browser_process(resources.browser_pid, thread_id)
                        
                    except Exception as browser_error:
                        self.logger.error(f"Thread {thread_id}: Browser cleanup error: {str(browser_error)}")
                    finally:
                        resources.driver_instance = None
                        resources.browser_pid = None
                
                # Release proxy reservation and cleanup thread extension
                if resources.proxy_id:
                    try:
                        self.logger.info(f"Thread {thread_id}: Releasing proxy {resources.proxy_id}")
                        self.proxy_manager.release_proxy_reservation(resources.proxy_id)

                        # Cleanup thread-specific extension
                        self.proxy_manager.cleanup_thread_extension(thread_id)

                    except Exception as proxy_error:
                        self.logger.error(f"Thread {thread_id}: Proxy cleanup error: {str(proxy_error)}")
                    finally:
                        resources.proxy_id = None
                        resources.proxy_config = None
                
                # Update cleanup tracking
                resources.last_cleanup = datetime.now()
                resources.cleanup_count += 1
                resources.is_active = False
                
                # Force garbage collection
                gc.collect()
                
                self.logger.info(f"Thread {thread_id}: Resource cleanup completed")
                
            except Exception as e:
                self.logger.error(f"Thread {thread_id}: Cleanup failed: {str(e)}")
    
    def deallocate_thread_resources(self, thread_id: int):
        """
        Completely deallocate and remove thread resources
        
        Args:
            thread_id: Thread identifier
        """
        with self._resources_lock:
            if thread_id not in self._thread_resources:
                return
            
            # Force cleanup first
            self.cleanup_thread_resources(thread_id, force=True)
            
            # Remove from tracking
            del self._thread_resources[thread_id]
            self._active_threads.discard(thread_id)
            
            self.logger.info(f"Thread {thread_id}: Resources deallocated")
    
    def mark_thread_inactive(self, thread_id: int):
        """Mark a thread as inactive for cleanup"""
        with self._resources_lock:
            if thread_id in self._thread_resources:
                self._thread_resources[thread_id].is_active = False
                self._active_threads.discard(thread_id)
                self.logger.info(f"Thread {thread_id}: Marked as inactive")
    
    def get_thread_resources(self, thread_id: int) -> Optional[ThreadResources]:
        """Get resources for a specific thread"""
        with self._resources_lock:
            return self._thread_resources.get(thread_id)
    
    def get_active_thread_count(self) -> int:
        """Get number of active threads"""
        with self._resources_lock:
            return len(self._active_threads)
    
    def get_total_memory_usage(self) -> float:
        """Get total memory usage across all threads in MB"""
        with self._resources_lock:
            total_memory = 0.0
            for thread_id in self._thread_resources:
                memory = self._get_thread_memory_usage(thread_id)
                total_memory += memory
            return total_memory
    
    def _get_thread_memory_usage(self, thread_id: int) -> float:
        """Get memory usage for a specific thread in MB"""
        try:
            resources = self._thread_resources.get(thread_id)
            if not resources or not resources.browser_pid:
                return 0.0
            
            process = psutil.Process(resources.browser_pid)
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)  # Convert to MB
            
            # Update stored memory usage
            resources.memory_usage_mb = memory_mb
            return memory_mb
            
        except (psutil.NoSuchProcess, psutil.AccessDenied, Exception):
            return 0.0
    
    def _cleanup_browser_process(self, pid: int, thread_id: int):
        """Cleanup browser process by PID"""
        try:
            process = psutil.Process(pid)
            if process.is_running():
                self.logger.info(f"Thread {thread_id}: Terminating browser process {pid}")
                process.terminate()
                
                # Wait for graceful termination
                try:
                    process.wait(timeout=10)
                except psutil.TimeoutExpired:
                    self.logger.warning(f"Thread {thread_id}: Force killing browser process {pid}")
                    process.kill()
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            self.logger.debug(f"Thread {thread_id}: Browser process {pid} cleanup: {str(e)}")
    
    def start_resource_monitoring(self):
        """Start background resource monitoring and cleanup"""
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            return
        
        self._stop_cleanup.clear()
        self._cleanup_thread = threading.Thread(
            target=self._resource_monitor_worker,
            name="ResourceMonitor",
            daemon=True
        )
        self._cleanup_thread.start()
        self.logger.info("Resource monitoring started")
    
    def stop_resource_monitoring(self):
        """Stop background resource monitoring"""
        self._stop_cleanup.set()
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
        self.logger.info("Resource monitoring stopped")
    
    def _resource_monitor_worker(self):
        """Background worker for resource monitoring and cleanup"""
        while not self._stop_cleanup.is_set():
            try:
                self._perform_maintenance_cleanup()
                time.sleep(self.cleanup_interval_seconds)
            except Exception as e:
                self.logger.error(f"Resource monitor error: {str(e)}")
                time.sleep(30)  # Wait before retrying
    
    def _perform_maintenance_cleanup(self):
        """Perform maintenance cleanup of inactive resources"""
        with self._resources_lock:
            inactive_threads = []
            
            for thread_id, resources in self._thread_resources.items():
                if not resources.is_active:
                    # Check if thread has been inactive for cleanup interval
                    if (resources.last_cleanup is None or 
                        (datetime.now() - resources.last_cleanup).total_seconds() > self.cleanup_interval_seconds):
                        inactive_threads.append(thread_id)
                
                # Check memory usage
                memory_usage = self._get_thread_memory_usage(thread_id)
                if memory_usage > self.max_memory_per_thread_mb:
                    self.logger.warning(f"Thread {thread_id}: High memory usage: {memory_usage:.1f}MB")
            
            # Cleanup inactive threads
            for thread_id in inactive_threads:
                self.cleanup_thread_resources(thread_id, force=True)
    
    def cleanup_all_resources(self):
        """Cleanup all thread resources (emergency cleanup)"""
        with self._resources_lock:
            thread_ids = list(self._thread_resources.keys())
            
        for thread_id in thread_ids:
            try:
                self.deallocate_thread_resources(thread_id)
            except Exception as e:
                self.logger.error(f"Error cleaning up thread {thread_id}: {str(e)}")
        
        # Cleanup all thread extensions
        try:
            self.proxy_manager.cleanup_all_thread_extensions()
        except Exception as e:
            self.logger.error(f"Error cleaning up thread extensions: {str(e)}")

        self.logger.info("All thread resources cleaned up")
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """Get summary of current resource usage"""
        with self._resources_lock:
            return {
                'active_threads': len(self._active_threads),
                'total_threads': len(self._thread_resources),
                'total_memory_mb': self.get_total_memory_usage(),
                'thread_details': {
                    tid: {
                        'name': res.thread_name,
                        'proxy_id': res.proxy_id,
                        'memory_mb': res.memory_usage_mb,
                        'is_active': res.is_active,
                        'cleanup_count': res.cleanup_count
                    }
                    for tid, res in self._thread_resources.items()
                }
            }

    def configure_captcha_coordination(self, inter_thread_delay: int = 60,
                                     max_wait_time: int = 300, focus_management: bool = True):
        """
        Configure captcha coordination parameters

        Args:
            inter_thread_delay: Seconds to delay between threads starting captcha operations
            max_wait_time: Maximum seconds a thread will wait for captcha lock
            focus_management: Whether to manage browser window focus during captcha solving
        """
        self.captcha_coordinator.configure(inter_thread_delay, max_wait_time, focus_management)
        self.logger.info(f"Configured captcha coordination: delay={inter_thread_delay}s, "
                        f"max_wait={max_wait_time}s, focus={focus_management}")

    def acquire_captcha_lock(self, thread_id: int):
        """
        Acquire captcha solving lock for a thread

        Args:
            thread_id: Thread identifier

        Returns:
            Context manager for captcha lock
        """
        with self._resources_lock:
            if thread_id not in self._thread_resources:
                raise ValueError(f"Thread {thread_id}: No resources allocated")

            resources = self._thread_resources[thread_id]
            browser_title = None

            # Try to get browser window title for focus management
            if resources.driver_instance and hasattr(resources.driver_instance, 'browser'):
                try:
                    browser_title = resources.driver_instance.browser.get_title()
                except:
                    browser_title = f"GMX-Worker-{thread_id}"

            return self.captcha_coordinator.acquire_captcha_lock(
                thread_id, resources.thread_name, browser_title
            )

    def get_captcha_status(self) -> dict:
        """Get current captcha coordination status"""
        return self.captcha_coordinator.get_status()
