"""
Dynamic Proxy Management System for GMX Account Creation

This module handles proxy rotation, cooldown tracking, and dynamic proxy configuration
for the Chrome extension used in GMX account creation.
"""

import json
import os
import time
import random
import logging
import threading
from typing import Dict, List, Optional, Tuple, Set
from datetime import datetime, timedelta


class ProxyManager:
    """Manages proxy rotation with cooldown periods and usage tracking"""
    
    def __init__(self, proxy_file: str = "proxy.txt", usage_file: str = "proxy_usage.json",
                 cooldown_minutes: int = 30):
        """
        Initialize the proxy manager with thread-safety support

        Args:
            proxy_file: Path to file containing proxy configurations
            usage_file: Path to file tracking proxy usage
            cooldown_minutes: Cooldown period in minutes before reusing a proxy
        """
        self.proxy_file = proxy_file
        self.usage_file = usage_file
        self.cooldown_minutes = cooldown_minutes
        self.logger = logging.getLogger("ProxyManager")

        # Thread synchronization
        self._lock = threading.RLock()  # Reentrant lock for nested calls
        self._reserved_proxies: Set[str] = set()  # Track reserved proxy IDs

        # Extension paths - base template
        self.base_extension_path = os.path.join("extensions", "shared_proxy_extension")
        self.base_background_js_path = os.path.join(self.base_extension_path, "background.js")

        # Thread-specific extension tracking
        self._thread_extensions: Dict[int, str] = {}  # thread_id -> extension_path
        self._extension_lock = threading.Lock()

        # Load proxies and usage data
        self.proxies = self._load_proxies()
        self.usage_data = self._load_usage_data()

        self.logger.info(f"ProxyManager initialized with {len(self.proxies)} proxies")

    def create_thread_extension(self, thread_id: int) -> str:
        """
        Create a thread-specific proxy extension directory

        Args:
            thread_id: Thread identifier

        Returns:
            Path to thread-specific extension directory
        """
        with self._extension_lock:
            if thread_id in self._thread_extensions:
                return self._thread_extensions[thread_id]

            # Create thread-specific extension directory
            thread_extension_path = os.path.join("extensions", f"proxy_extension_thread_{thread_id}")

            try:
                # Create directory if it doesn't exist
                os.makedirs(thread_extension_path, exist_ok=True)

                # Copy manifest.json from base extension
                base_manifest = os.path.join(self.base_extension_path, "manifest.json")
                thread_manifest = os.path.join(thread_extension_path, "manifest.json")

                if os.path.exists(base_manifest):
                    import shutil
                    shutil.copy2(base_manifest, thread_manifest)

                # Create thread-specific background.js
                thread_background_js = os.path.join(thread_extension_path, "background.js")
                self._create_thread_background_js(thread_background_js)

                # Store the extension path
                self._thread_extensions[thread_id] = thread_extension_path

                self.logger.info(f"Created thread-specific extension for thread {thread_id}: {thread_extension_path}")
                return thread_extension_path

            except Exception as e:
                self.logger.error(f"Failed to create thread extension for thread {thread_id}: {str(e)}")
                raise

    def _create_thread_background_js(self, background_js_path: str):
        """Create a thread-specific background.js file"""
        try:
            # Read the base background.js template
            with open(self.base_background_js_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Write to thread-specific location
            with open(background_js_path, 'w', encoding='utf-8') as f:
                f.write(content)

        except Exception as e:
            self.logger.error(f"Failed to create thread background.js: {str(e)}")
            raise

    def _load_proxies(self) -> List[Dict[str, str]]:
        """Load proxy configurations from proxy.txt"""
        proxies = []
        
        if not os.path.exists(self.proxy_file):
            self.logger.warning(f"Proxy file {self.proxy_file} not found")
            return proxies
        
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    parts = line.split(':')
                    if len(parts) != 4:
                        self.logger.warning(f"Invalid proxy format on line {line_num}: {line}")
                        continue
                    
                    proxy = {
                        'host': parts[0].strip(),
                        'port': int(parts[1].strip()),
                        'username': parts[2].strip(),
                        'password': parts[3].strip(),
                        'id': f"{parts[0]}:{parts[1]}:{parts[2]}"
                    }
                    proxies.append(proxy)
            
            self.logger.info(f"Loaded {len(proxies)} proxies from {self.proxy_file}")
            
        except Exception as e:
            self.logger.error(f"Error loading proxies: {str(e)}")
        
        return proxies
    
    def _load_usage_data(self) -> Dict[str, Dict]:
        """Load proxy usage tracking data"""
        if not os.path.exists(self.usage_file):
            return {}
        
        try:
            with open(self.usage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.logger.info(f"Loaded usage data for {len(data)} proxies")
                return data
        except Exception as e:
            self.logger.error(f"Error loading usage data: {str(e)}")
            return {}
    
    def _save_usage_data(self):
        """Save proxy usage tracking data"""
        try:
            with open(self.usage_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_data, f, indent=2, default=str)
            self.logger.debug("Usage data saved successfully")
        except Exception as e:
            self.logger.error(f"Error saving usage data: {str(e)}")
    
    def _is_proxy_available(self, proxy_id: str) -> bool:
        """Check if a proxy is available (not in cooldown)"""
        if proxy_id not in self.usage_data:
            return True
        
        last_used = self.usage_data[proxy_id].get('last_used')
        if not last_used:
            return True
        
        # Parse last used time
        try:
            if isinstance(last_used, str):
                last_used_time = datetime.fromisoformat(last_used)
            else:
                last_used_time = datetime.fromtimestamp(last_used)
            
            cooldown_end = last_used_time + timedelta(minutes=self.cooldown_minutes)
            return datetime.now() >= cooldown_end
            
        except Exception as e:
            self.logger.warning(f"Error parsing last used time for {proxy_id}: {e}")
            return True
    
    def get_available_proxies(self) -> List[Dict[str, str]]:
        """Get list of proxies that are not in cooldown (thread-safe)"""
        with self._lock:
            return self._get_available_proxies_internal()

    def _get_available_proxies_internal(self) -> List[Dict[str, str]]:
        """Internal method to get available proxies (assumes lock is held)"""
        available = []
        for proxy in self.proxies:
            if self._is_proxy_available(proxy['id']) and proxy['id'] not in self._reserved_proxies:
                available.append(proxy)

        self.logger.info(f"Found {len(available)} available proxies out of {len(self.proxies)} (reserved: {len(self._reserved_proxies)})")
        return available
    
    def select_next_proxy(self, strategy: str = "least_used", reserve: bool = True) -> Optional[Dict[str, str]]:
        """
        Select the next proxy to use based on strategy (thread-safe)

        Args:
            strategy: Selection strategy - "least_used", "random", or "sequential"
            reserve: Whether to reserve the proxy for exclusive use

        Returns:
            Selected proxy configuration or None if no proxies available
        """
        with self._lock:
            available_proxies = self._get_available_proxies_internal()

            if not available_proxies:
                self.logger.warning("No available proxies found")
                return None

            if strategy == "random":
                selected = random.choice(available_proxies)
            elif strategy == "sequential":
                # Sort by last used time (oldest first)
                available_proxies.sort(key=lambda p: self.usage_data.get(p['id'], {}).get('last_used', '1970-01-01'))
                selected = available_proxies[0]
            else:  # least_used (default)
                # Sort by usage count (least used first)
                available_proxies.sort(key=lambda p: self.usage_data.get(p['id'], {}).get('usage_count', 0))
                selected = available_proxies[0]

            # Reserve the proxy if requested
            if reserve and selected:
                self._reserved_proxies.add(selected['id'])
                self.logger.info(f"Reserved proxy: {selected['id']} using {strategy} strategy")
            else:
                self.logger.info(f"Selected proxy: {selected['id']} using {strategy} strategy")

            return selected
    
    def mark_proxy_used(self, proxy_id: str, success: bool = True):
        """Mark a proxy as used and update usage statistics (thread-safe)"""
        with self._lock:
            if proxy_id not in self.usage_data:
                self.usage_data[proxy_id] = {
                    'usage_count': 0,
                    'success_count': 0,
                    'failure_count': 0,
                    'last_used': None
                }

            self.usage_data[proxy_id]['usage_count'] += 1
            self.usage_data[proxy_id]['last_used'] = datetime.now().isoformat()

            if success:
                self.usage_data[proxy_id]['success_count'] += 1
            else:
                self.usage_data[proxy_id]['failure_count'] += 1

            # Remove from reserved proxies
            self._reserved_proxies.discard(proxy_id)

            self._save_usage_data()
            self.logger.info(f"Marked proxy {proxy_id} as used (success: {success})")

    def release_proxy_reservation(self, proxy_id: str):
        """Release a proxy reservation (thread-safe)"""
        with self._lock:
            self._reserved_proxies.discard(proxy_id)
            self.logger.info(f"Released reservation for proxy: {proxy_id}")

    def get_reserved_proxies(self) -> Set[str]:
        """Get currently reserved proxy IDs (thread-safe)"""
        with self._lock:
            return self._reserved_proxies.copy()

    def update_extension_proxy(self, proxy: Dict[str, str], thread_id: int = None) -> bool:
        """
        Update the Chrome extension's proxy configuration by modifying background.js

        Args:
            proxy: Proxy configuration dictionary
            thread_id: Thread identifier for thread-specific extension (optional)

        Returns:
            True if update successful, False otherwise
        """
        try:
            # Create proxy configuration for the extension
            proxy_config = {
                'scheme': 'http',
                'host': proxy['host'],
                'port': proxy['port'],
                'username': proxy['username'],
                'password': proxy['password']
            }

            # Update the appropriate background.js file
            if thread_id is not None:
                # Update thread-specific extension
                self._update_thread_background_js(proxy_config, thread_id)
                self.logger.info(f"Updated thread {thread_id} extension proxy configuration: {proxy['id']}")
            else:
                # Update shared extension (legacy behavior)
                self._update_background_js(proxy_config, self.base_background_js_path)
                self.logger.info(f"Updated shared extension proxy configuration: {proxy['id']}")

            return True

        except Exception as e:
            self.logger.error(f"Error updating extension proxy: {str(e)}")
            return False
    
    def _update_background_js(self, proxy_config: Dict[str, str], background_js_path: str):
        """Update the background.js file with new proxy configuration by replacing PROXY_CONFIG"""
        try:
            # Read current background.js
            with open(background_js_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Create new PROXY_CONFIG object with proper formatting
            new_config = f"""const PROXY_CONFIG = {{
  scheme: "{proxy_config['scheme']}",
  host: "{proxy_config['host']}",
  port: {proxy_config['port']},
  username: "{proxy_config['username']}",
  password: "{proxy_config['password']}"
}};"""

            # Replace the existing PROXY_CONFIG using regex
            import re
            # Match the PROXY_CONFIG object including multi-line content
            pattern = r'const PROXY_CONFIG = \{[^}]*\};'

            if re.search(pattern, content, flags=re.DOTALL):
                updated_content = re.sub(pattern, new_config, content, flags=re.DOTALL)

                # Write updated content back to file
                with open(background_js_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)

                self.logger.debug(f"background.js updated with new proxy: {proxy_config['host']}:{proxy_config['port']}")
            else:
                self.logger.warning("Could not find PROXY_CONFIG pattern in background.js, attempting to create default config")
                # Try to create a basic proxy configuration if pattern not found
                default_config = f"""
// Proxy configuration
const PROXY_CONFIG = {{
    mode: "fixed_servers",
    rules: {{
        singleProxy: {{
            scheme: "{proxy_config['scheme']}",
            host: "{proxy_config['host']}",
            port: {proxy_config['port']}
        }}
    }}
}};

// Set proxy authentication
chrome.webRequest.onAuthRequired.addListener(
    function(details) {{
        return {{
            authCredentials: {{
                username: "{proxy_config['username']}",
                password: "{proxy_config['password']}"
            }}
        }};
    }},
    {{urls: ["<all_urls>"]}},
    ["blocking"]
);

// Apply proxy settings
chrome.proxy.settings.set({{
    value: PROXY_CONFIG,
    scope: 'regular'
}});
"""

                # Write the default configuration
                with open(background_js_path, 'w', encoding='utf-8') as f:
                    f.write(default_config)

                self.logger.info(f"Created default proxy configuration for {proxy_config['host']}:{proxy_config['port']}")

        except Exception as e:
            self.logger.error(f"Error updating background.js: {str(e)}")
            raise

    def _update_thread_background_js(self, proxy_config: Dict[str, str], thread_id: int):
        """Update thread-specific background.js file"""
        try:
            # Get or create thread extension
            thread_extension_path = self.create_thread_extension(thread_id)
            thread_background_js = os.path.join(thread_extension_path, "background.js")

            # Update the thread-specific background.js
            self._update_background_js(proxy_config, thread_background_js)

        except Exception as e:
            self.logger.error(f"Error updating thread {thread_id} background.js: {str(e)}")
            raise
    
    def get_proxy_stats(self) -> Dict[str, Dict]:
        """Get usage statistics for all proxies"""
        stats = {}
        for proxy in self.proxies:
            proxy_id = proxy['id']
            usage = self.usage_data.get(proxy_id, {})
            
            stats[proxy_id] = {
                'host': proxy['host'],
                'port': proxy['port'],
                'usage_count': usage.get('usage_count', 0),
                'success_count': usage.get('success_count', 0),
                'failure_count': usage.get('failure_count', 0),
                'last_used': usage.get('last_used'),
                'available': self._is_proxy_available(proxy_id)
            }
        
        return stats
    
    def rotate_proxy(self, strategy: str = "least_used", reserve: bool = True, thread_id: int = None) -> Optional[Dict[str, str]]:
        """
        Main method to rotate to the next available proxy (thread-safe)

        Args:
            strategy: Selection strategy for proxy rotation
            reserve: Whether to reserve the proxy for exclusive use
            thread_id: Thread identifier for thread-specific extension (optional)

        Returns:
            Selected proxy configuration or None if no proxies available
        """
        max_retries = 3
        retry_delay = 0.5

        for attempt in range(max_retries):
            # Select next proxy with reservation
            selected_proxy = self.select_next_proxy(strategy, reserve=reserve)

            if selected_proxy:
                break

            if attempt < max_retries - 1:
                self.logger.warning(f"Proxy selection attempt {attempt + 1} failed, retrying in {retry_delay}s")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff

        if not selected_proxy:
            self.logger.error("Failed to obtain proxy after all retry attempts")
            return None

        # Update extension configuration (thread-specific if thread_id provided)
        if self.update_extension_proxy(selected_proxy, thread_id=thread_id):
            return selected_proxy
        else:
            # If extension update failed, release the reservation
            if reserve:
                self.release_proxy_reservation(selected_proxy['id'])
            return None

    def get_thread_extension_path(self, thread_id: int) -> Optional[str]:
        """
        Get the extension path for a specific thread

        Args:
            thread_id: Thread identifier

        Returns:
            Extension path or None if not found
        """
        with self._extension_lock:
            return self._thread_extensions.get(thread_id)

    def cleanup_thread_extension(self, thread_id: int):
        """
        Clean up thread-specific extension directory

        Args:
            thread_id: Thread identifier
        """
        with self._extension_lock:
            if thread_id in self._thread_extensions:
                extension_path = self._thread_extensions[thread_id]
                try:
                    import shutil
                    if os.path.exists(extension_path):
                        shutil.rmtree(extension_path)
                        self.logger.info(f"Cleaned up thread extension for thread {thread_id}")

                    del self._thread_extensions[thread_id]

                except Exception as e:
                    self.logger.error(f"Failed to cleanup thread extension for thread {thread_id}: {str(e)}")

    def cleanup_all_thread_extensions(self):
        """Clean up all thread-specific extensions"""
        with self._extension_lock:
            thread_ids = list(self._thread_extensions.keys())
            for thread_id in thread_ids:
                self.cleanup_thread_extension(thread_id)
