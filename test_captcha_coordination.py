"""
Test Suite for Captcha Coordination System

This module provides comprehensive tests for the captcha coordination system,
ensuring proper thread synchronization and conflict prevention.
"""

import unittest
import threading
import time
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from captcha_coordinator import Captcha<PERSON>oordinator, CaptchaSession
from thread_resource_manager import ThreadResourceManager
from proxy_manager import ProxyManager


class TestCaptchaCoordinator(unittest.TestCase):
    """Test cases for CaptchaCoordinator class"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Reset singleton instance for each test
        CaptchaCoordinator._instance = None
        self.coordinator = CaptchaCoordinator()
        
        # Configure for testing
        self.coordinator.configure(
            inter_thread_delay=1,  # Short delay for testing
            max_wait_time=10,      # Short timeout for testing
            focus_management=False  # Disable focus management for tests
        )
    
    def tearDown(self):
        """Clean up after tests"""
        # Reset singleton
        CaptchaCoordinator._instance = None
    
    def test_singleton_pattern(self):
        """Test that CaptchaCoordinator follows singleton pattern"""
        coordinator1 = CaptchaCoordinator()
        coordinator2 = CaptchaCoordinator()
        
        self.assertIs(coordinator1, coordinator2)
        self.assertIs(coordinator1, self.coordinator)
    
    def test_configuration(self):
        """Test coordinator configuration"""
        self.coordinator.configure(
            inter_thread_delay=30,
            max_wait_time=120,
            focus_management=True
        )
        
        self.assertEqual(self.coordinator.inter_thread_delay_seconds, 30)
        self.assertEqual(self.coordinator.max_captcha_wait_seconds, 120)
        self.assertTrue(self.coordinator.focus_management_enabled)
    
    def test_single_thread_captcha_lock(self):
        """Test basic captcha lock acquisition and release"""
        thread_id = 1
        thread_name = "Test-Thread-1"
        
        with self.coordinator.acquire_captcha_lock(thread_id, thread_name) as session:
            self.assertIsInstance(session, CaptchaSession)
            self.assertEqual(session.thread_id, thread_id)
            self.assertEqual(session.thread_name, thread_name)
            self.assertIsNotNone(session.started_at)
            
            # Verify coordinator shows active session
            status = self.coordinator.get_status()
            self.assertIsNotNone(status['active_session'])
            self.assertEqual(status['active_session']['thread_id'], thread_id)
        
        # Verify session is cleaned up after context exit
        status = self.coordinator.get_status()
        self.assertIsNone(status['active_session'])
    
    def test_concurrent_captcha_coordination(self):
        """Test that only one thread can solve captcha at a time"""
        results = {}
        start_times = {}
        end_times = {}
        
        def captcha_worker(thread_id):
            """Worker function that simulates captcha solving"""
            thread_name = f"Worker-{thread_id}"
            
            try:
                with self.coordinator.acquire_captcha_lock(thread_id, thread_name) as session:
                    start_times[thread_id] = time.time()
                    results[thread_id] = 'acquired'
                    
                    # Simulate captcha solving time
                    time.sleep(0.5)
                    
                    end_times[thread_id] = time.time()
                    results[thread_id] = 'completed'
                    
            except Exception as e:
                results[thread_id] = f'error: {str(e)}'
        
        # Start multiple threads simultaneously
        threads = []
        for i in range(3):
            thread = threading.Thread(target=captcha_worker, args=(i,))
            threads.append(thread)
        
        # Start all threads at roughly the same time
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=15)
        
        # Verify all threads completed successfully
        for i in range(3):
            self.assertEqual(results[i], 'completed')
        
        # Verify threads were serialized (no overlap in execution)
        sorted_threads = sorted(start_times.keys(), key=lambda x: start_times[x])
        
        for i in range(len(sorted_threads) - 1):
            current_thread = sorted_threads[i]
            next_thread = sorted_threads[i + 1]
            
            # Current thread should finish before next thread starts
            self.assertLessEqual(
                end_times[current_thread], 
                start_times[next_thread],
                f"Thread {current_thread} overlapped with thread {next_thread}"
            )
    
    def test_captcha_timeout(self):
        """Test timeout behavior when captcha lock cannot be acquired"""
        # Configure very short timeout
        self.coordinator.configure(max_wait_time=1)
        
        # First thread acquires lock
        def long_captcha_worker():
            with self.coordinator.acquire_captcha_lock(1, "Long-Worker"):
                time.sleep(3)  # Hold lock longer than timeout
        
        # Start first thread
        long_thread = threading.Thread(target=long_captcha_worker)
        long_thread.start()
        
        # Give first thread time to acquire lock
        time.sleep(0.1)
        
        # Second thread should timeout
        with self.assertRaises(TimeoutError):
            with self.coordinator.acquire_captcha_lock(2, "Timeout-Worker"):
                pass
        
        long_thread.join()
    
    def test_inter_thread_delay(self):
        """Test inter-thread delay functionality"""
        self.coordinator.configure(inter_thread_delay=2)
        
        start_times = {}
        
        def delayed_worker(thread_id):
            start_times[thread_id] = time.time()
            with self.coordinator.acquire_captcha_lock(thread_id, f"Delayed-{thread_id}"):
                pass
        
        # Start threads with different IDs
        threads = []
        for i in range(3):
            thread = threading.Thread(target=delayed_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Verify delays were applied (thread 1 should start ~2s after thread 0)
        if len(start_times) >= 2:
            delay = start_times[1] - start_times[0]
            self.assertGreaterEqual(delay, 1.5)  # Allow some tolerance
    
    def test_status_reporting(self):
        """Test status reporting functionality"""
        # Initial status
        status = self.coordinator.get_status()
        self.assertIsNone(status['active_session'])
        self.assertEqual(status['waiting_threads'], 0)
        
        # Status during active session
        with self.coordinator.acquire_captcha_lock(1, "Status-Test"):
            status = self.coordinator.get_status()
            self.assertIsNotNone(status['active_session'])
            self.assertEqual(status['active_session']['thread_id'], 1)
        
        # Status after session
        status = self.coordinator.get_status()
        self.assertIsNone(status['active_session'])


class TestThreadResourceManagerCaptchaIntegration(unittest.TestCase):
    """Test integration between ThreadResourceManager and CaptchaCoordinator"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Reset coordinator singleton
        CaptchaCoordinator._instance = None
        
        # Create mock proxy manager
        self.mock_proxy_manager = Mock(spec=ProxyManager)
        self.mock_proxy_manager.get_proxy_for_thread.return_value = {
            'proxy_id': 'test-proxy-1',
            'proxy_config': {'http': 'http://test:8080'}
        }
        
        # Create resource manager
        self.resource_manager = ThreadResourceManager(self.mock_proxy_manager)
    
    def tearDown(self):
        """Clean up after tests"""
        self.resource_manager.cleanup_all_resources()
        CaptchaCoordinator._instance = None
    
    def test_captcha_coordination_configuration(self):
        """Test captcha coordination configuration through resource manager"""
        self.resource_manager.configure_captcha_coordination(
            inter_thread_delay=45,
            max_wait_time=180,
            focus_management=False
        )
        
        coordinator = self.resource_manager.captcha_coordinator
        self.assertEqual(coordinator.inter_thread_delay_seconds, 45)
        self.assertEqual(coordinator.max_captcha_wait_seconds, 180)
        self.assertFalse(coordinator.focus_management_enabled)
    
    @patch('gmx.Driver')
    def test_captcha_lock_acquisition(self, mock_driver_class):
        """Test captcha lock acquisition through resource manager"""
        # Set up mock driver
        mock_driver = Mock()
        mock_driver_class.return_value = mock_driver
        
        # Allocate thread resources
        thread_id = 1
        resources = self.resource_manager.allocate_thread_resources(thread_id, "Test-Thread")
        
        # Test captcha lock acquisition
        with self.resource_manager.acquire_captcha_lock(thread_id) as session:
            self.assertIsInstance(session, CaptchaSession)
            self.assertEqual(session.thread_id, thread_id)
    
    def test_captcha_status_retrieval(self):
        """Test captcha status retrieval through resource manager"""
        status = self.resource_manager.get_captcha_status()
        
        self.assertIn('active_session', status)
        self.assertIn('waiting_threads', status)
        self.assertIn('configuration', status)


class TestCaptchaCoordinationStressTest(unittest.TestCase):
    """Stress tests for captcha coordination system"""
    
    def setUp(self):
        """Set up test fixtures"""
        CaptchaCoordinator._instance = None
        self.coordinator = CaptchaCoordinator()
        self.coordinator.configure(
            inter_thread_delay=0.1,  # Very short for stress testing
            max_wait_time=30,
            focus_management=False
        )
    
    def tearDown(self):
        """Clean up after tests"""
        CaptchaCoordinator._instance = None
    
    def test_high_concurrency_stress(self):
        """Test system under high concurrency load"""
        num_threads = 10
        results = {}
        
        def stress_worker(thread_id):
            try:
                with self.coordinator.acquire_captcha_lock(thread_id, f"Stress-{thread_id}"):
                    # Simulate brief captcha solving
                    time.sleep(0.05)
                    results[thread_id] = 'success'
            except Exception as e:
                results[thread_id] = f'error: {str(e)}'
        
        # Use ThreadPoolExecutor for better thread management
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(stress_worker, i) for i in range(num_threads)]
            
            # Wait for all to complete
            for future in as_completed(futures, timeout=60):
                future.result()  # This will raise any exceptions
        
        # Verify all threads completed successfully
        for i in range(num_threads):
            self.assertEqual(results[i], 'success', f"Thread {i} failed: {results.get(i)}")
        
        # Verify final state is clean
        status = self.coordinator.get_status()
        self.assertIsNone(status['active_session'])
        self.assertEqual(status['waiting_threads'], 0)


if __name__ == '__main__':
    # Configure logging for tests
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run tests
    unittest.main(verbosity=2)
