"""
Captcha Coordination System

This module provides coordination for captcha solving operations in multithreaded environments.
It prevents conflicts when multiple threads attempt to use PyAutoGUI simultaneously for captcha solving.
"""

import threading
import time
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from contextlib import contextmanager


@dataclass
class CaptchaSession:
    """Information about an active captcha solving session"""
    thread_id: int
    thread_name: str
    started_at: datetime
    browser_window_title: Optional[str] = None
    captcha_type: Optional[str] = None


class CaptchaCoordinator:
    """
    Global coordinator for captcha solving operations across multiple threads.
    
    This class ensures that only one thread can solve captchas at a time,
    preventing PyAutoGUI conflicts and ensuring proper focus management.
    """
    
    _instance: Optional['CaptchaCoordinator'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Singleton pattern to ensure only one coordinator exists"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the captcha coordinator"""
        if hasattr(self, '_initialized'):
            return
            
        self.logger = logging.getLogger(f"{__name__}.CaptchaCoordinator")
        
        # Captcha solving coordination
        self._captcha_lock = threading.RLock()
        self._captcha_semaphore = threading.Semaphore(1)  # Only one captcha solver at a time
        self._active_session: Optional[CaptchaSession] = None
        
        # Thread coordination
        self._waiting_threads: Dict[int, datetime] = {}
        self._thread_delays: Dict[int, float] = {}
        
        # Configuration
        self.inter_thread_delay_seconds = 60  # 1 minute delay between threads
        self.max_captcha_wait_seconds = 300   # 5 minutes max wait for captcha lock
        self.focus_management_enabled = True
        
        # Statistics
        self._captcha_sessions_completed = 0
        self._total_wait_time_seconds = 0.0
        
        self._initialized = True
        self.logger.info("Captcha coordinator initialized")
    
    def configure(self, inter_thread_delay: int = 60, max_wait_time: int = 300, 
                  focus_management: bool = True):
        """
        Configure captcha coordination parameters
        
        Args:
            inter_thread_delay: Seconds to delay between threads starting captcha operations
            max_wait_time: Maximum seconds a thread will wait for captcha lock
            focus_management: Whether to manage browser window focus during captcha solving
        """
        with self._captcha_lock:
            self.inter_thread_delay_seconds = inter_thread_delay
            self.max_captcha_wait_seconds = max_wait_time
            self.focus_management_enabled = focus_management
            
            self.logger.info(f"Captcha coordinator configured: delay={inter_thread_delay}s, "
                           f"max_wait={max_wait_time}s, focus_mgmt={focus_management}")
    
    def _calculate_thread_delay(self, thread_id: int) -> float:
        """Calculate delay for thread based on thread ID and existing delays"""
        with self._captcha_lock:
            if thread_id in self._thread_delays:
                return self._thread_delays[thread_id]
            
            # Calculate staggered delay based on thread ID
            base_delay = thread_id * self.inter_thread_delay_seconds
            
            # Add some randomization to prevent exact timing conflicts
            import random
            jitter = random.uniform(-5, 5)  # ±5 seconds jitter
            
            delay = max(0, base_delay + jitter)
            self._thread_delays[thread_id] = delay
            
            return delay
    
    def _apply_inter_thread_delay(self, thread_id: int, thread_name: str):
        """Apply inter-thread delay before allowing captcha operations"""
        delay = self._calculate_thread_delay(thread_id)
        
        if delay > 0:
            self.logger.info(f"Thread {thread_name}: Applying captcha delay of {delay:.1f} seconds")
            time.sleep(delay)
    
    @contextmanager
    def acquire_captcha_lock(self, thread_id: int, thread_name: str, 
                           browser_window_title: Optional[str] = None):
        """
        Context manager to acquire exclusive captcha solving rights
        
        Args:
            thread_id: Thread identifier
            thread_name: Human-readable thread name
            browser_window_title: Title of browser window for focus management
            
        Yields:
            CaptchaSession object with session information
            
        Raises:
            TimeoutError: If unable to acquire lock within max_wait_time
        """
        session = None
        acquired = False
        wait_start = datetime.now()
        
        try:
            # Apply inter-thread delay
            self._apply_inter_thread_delay(thread_id, thread_name)
            
            # Record that this thread is waiting
            with self._captcha_lock:
                self._waiting_threads[thread_id] = datetime.now()
            
            self.logger.info(f"Thread {thread_name}: Requesting captcha lock...")
            
            # Try to acquire the semaphore with timeout
            acquired = self._captcha_semaphore.acquire(timeout=self.max_captcha_wait_seconds)
            
            if not acquired:
                wait_time = (datetime.now() - wait_start).total_seconds()
                raise TimeoutError(f"Thread {thread_name}: Failed to acquire captcha lock "
                                 f"within {self.max_captcha_wait_seconds} seconds "
                                 f"(waited {wait_time:.1f}s)")
            
            # Create captcha session
            with self._captcha_lock:
                session = CaptchaSession(
                    thread_id=thread_id,
                    thread_name=thread_name,
                    started_at=datetime.now(),
                    browser_window_title=browser_window_title
                )
                self._active_session = session
                
                # Remove from waiting list
                self._waiting_threads.pop(thread_id, None)
            
            wait_time = (session.started_at - wait_start).total_seconds()
            self.logger.info(f"Thread {thread_name}: Acquired captcha lock "
                           f"(waited {wait_time:.1f}s)")
            
            # Manage browser focus if enabled
            if self.focus_management_enabled and browser_window_title:
                self._bring_window_to_focus(browser_window_title, thread_name)
            
            yield session
            
        finally:
            # Clean up session and release lock
            if acquired:
                with self._captcha_lock:
                    if self._active_session and self._active_session.thread_id == thread_id:
                        session_duration = (datetime.now() - self._active_session.started_at).total_seconds()
                        self._captcha_sessions_completed += 1
                        self._total_wait_time_seconds += (self._active_session.started_at - wait_start).total_seconds()
                        
                        self.logger.info(f"Thread {thread_name}: Releasing captcha lock "
                                       f"(session duration: {session_duration:.1f}s)")
                        
                        self._active_session = None
                
                self._captcha_semaphore.release()
            
            # Remove from waiting list if still there
            with self._captcha_lock:
                self._waiting_threads.pop(thread_id, None)
    
    def _bring_window_to_focus(self, window_title: str, thread_name: str):
        """Bring browser window to focus for captcha solving"""
        try:
            import pyautogui
            import pygetwindow as gw
            
            # Find window by title (partial match)
            windows = gw.getWindowsWithTitle(window_title)
            if not windows:
                # Try to find any Chrome/browser window
                chrome_windows = gw.getWindowsWithTitle('Chrome')
                if chrome_windows:
                    windows = chrome_windows[:1]
            
            if windows:
                window = windows[0]
                if window.isMinimized:
                    window.restore()
                window.activate()
                time.sleep(0.5)  # Give time for window to come to focus
                
                self.logger.info(f"Thread {thread_name}: Brought browser window to focus")
            else:
                self.logger.warning(f"Thread {thread_name}: Could not find browser window '{window_title}'")
                
        except Exception as e:
            self.logger.warning(f"Thread {thread_name}: Failed to manage window focus: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of captcha coordination"""
        with self._captcha_lock:
            status = {
                'active_session': None,
                'waiting_threads': len(self._waiting_threads),
                'waiting_thread_ids': list(self._waiting_threads.keys()),
                'total_sessions_completed': self._captcha_sessions_completed,
                'average_wait_time_seconds': (
                    self._total_wait_time_seconds / max(1, self._captcha_sessions_completed)
                ),
                'configuration': {
                    'inter_thread_delay_seconds': self.inter_thread_delay_seconds,
                    'max_captcha_wait_seconds': self.max_captcha_wait_seconds,
                    'focus_management_enabled': self.focus_management_enabled
                }
            }
            
            if self._active_session:
                session_duration = (datetime.now() - self._active_session.started_at).total_seconds()
                status['active_session'] = {
                    'thread_id': self._active_session.thread_id,
                    'thread_name': self._active_session.thread_name,
                    'duration_seconds': session_duration,
                    'started_at': self._active_session.started_at.isoformat()
                }
            
            return status
    
    def is_captcha_active(self) -> bool:
        """Check if any thread is currently solving a captcha"""
        with self._captcha_lock:
            return self._active_session is not None
    
    def get_active_thread(self) -> Optional[str]:
        """Get the name of the thread currently solving captcha"""
        with self._captcha_lock:
            return self._active_session.thread_name if self._active_session else None


# Global instance
captcha_coordinator = CaptchaCoordinator()
